"use client";

import { memo, useEffect, useMemo } from "react";
import { usePathname } from "next/navigation";
import {
	Database,
	// Settings,
	Menu,
	// SquareUserRound,
	FileText,
	// CircleCheck,
	// Building2,
	// UserPlus2,
	BookOpenText,
	// ClipboardList,
	LayoutGrid,
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import singHealthLogo from "../../../assests/SingHealth-Logo.png";
import { useMediaQuery, useTheme } from "@mui/material";
import { tailwindStyles } from "../../../styles/tailwindStyles";
import type { RootState } from "../../../store/store";
import { useAppSelector } from "../../../store/store";
import { superadmin } from "../../../constants/fieldtype";

interface SidebarProps {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, setIsOpen }) => {
	const theme = useTheme();
	const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
	const pathname = usePathname();
	const isSuperAdmin = useAppSelector(
		(state: RootState) =>
			Array.isArray(superadmin)
				? superadmin.includes(state?.user?.userDetail?.role?.name)
				: state?.user?.userDetail?.role?.name === superadmin,
	) as boolean | undefined;
	console.log();
	
	const datasetListDetails = useAppSelector(
		(state: RootState) => state?.accessRights?.accessRightsList?.modules,
	) as
		| {
				module_id: string;
				module_name: string;
				permissions: { permission_id: string; permission_name: string }[];
		  }[]
		| undefined;

		
		
	// Automatically close on mobile
	useEffect(() => {
		setIsOpen(!isMobile);
	}, [isMobile, setIsOpen]);

	const handleToggleSidebar = () => {
		setIsOpen((prev) => !prev);
	};

	const handleLinkClick = () => {
		if (isMobile) {
			setIsOpen(false);
		}
	};

	const isActive = (path: string): boolean => {
		return (
			(pathname === "/" && path === "/project-results") || pathname === path
		);
	};

	const allMenuItems = useMemo(
		() => [
			{ path: "/dashboard", icon: LayoutGrid, label: "Dashboard" },
			{ path: "/datasets", icon: Database, label: "Datasets" },
			// { path: "/organizations", icon: Building2, label: "Organizations" },
			// { path: "/request-approval", icon: CircleCheck, label: "Request Approval" },
			// { path: "/role-access", icon: UserPlus2, label: "Role & Access" },
			{ path: "/project-results", icon: BookOpenText, label: "Project Dockets" },		
			// { path: "/user-management", icon: SquareUserRound, label: "User Management" },
			// { path: "/audit-logs", icon: ClipboardList, label: "Audit Logs" },
			{ path: "/forms", icon: FileText, label: "Forms" },		
			// { path: "/settings", icon: Settings, label: "Settings" },
		],
		[],
	);

	const moduleNameToPathMap = useMemo(() => {
		const map: Record<string, string> = {};
		allMenuItems.map((item) => {
			map[item.label] = item.path;
		});
		return map;
	}, [allMenuItems]);

	const allowedPaths = useMemo(() => {
		return (datasetListDetails || [])
			.map((item) => moduleNameToPathMap[item.module_name])
			.filter(Boolean);
	}, [datasetListDetails, moduleNameToPathMap]);

	const filteredMenuItems = useMemo(() => {
	if (isSuperAdmin) {
		return allMenuItems;
	}
	return allMenuItems.filter((item) => allowedPaths.includes(item.path));
}, [isSuperAdmin, allowedPaths, allMenuItems]);

	return (
		<div
			className={`h-full flex flex-col bg-white transition-all duration-300 ${
				isOpen ? "w-64" : "w-20"
			}`}
		>
			{/* Logo & Toggle */}
				<div className="flex items-center  mt-0 md:mt-1 border-b border-gray-200 bg-white " style={{ boxShadow: '0 7px 4px -2px rgb(246,247,248)',padding:"16.2px"}} >
				<button
					type="button"
					onClick={handleToggleSidebar}
					className="p-1 rounded-md focus:outline-none"
				>
					<Menu className="w-6 h-6 text-[#7C8CA1]" />
				</button>
				{isOpen && (
					<Image
						src={singHealthLogo}
						alt="SingHealth Logo"
						width={120}
						height={40}
						priority
						className="ml-3 object-contain"
					/>
				)}
			</div>

			{/* Navigation */}
			<nav className="flex-1 overflow-y-auto p-2 text-sm">
				{filteredMenuItems.map((item) => (
					<Link
						key={item.path}
						href={item.path}
						onClick={handleLinkClick}
						prefetch={false} //  disable prefetch to improve perf if needed
					>
						<div
							className={`flex items-center p-2 mt-4 rounded-md cursor-pointer transition-colors duration-200 ${
								isActive(item.path)
									? `${tailwindStyles.sidebarName} font-bold`
									: "text-[#7C8CA1] hover:bg-gray-100"
							}`}
						>
							<item.icon className="w-5 h-5 min-w-[20px]" />
							{isOpen && <span className="ml-2">{item.label}</span>}
						</div>
					</Link>
				))}
			</nav>
		</div>
	);
};

//  Memoized component to avoid re-renders unless props change
export default memo(Sidebar);
