"use client";

import { Box, Typography, Table, TableHead, TableRow, TableCell, TableBody, Button } from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { useState } from "react";
import { useRouter } from "next/navigation";


const tableData = [
  { category: "Classification", metric: "Accuracy", value: 0.89, threshold: "≥0.85", description: "(TP + TN)/(Total Samples)" },
  { category: "Classification", metric: "F1-Score", value: 0.79, threshold: "≥0.80", description: "2*(Precision*Recall)/(Precision+Recall)" },
  { category: "Segmentation", metric: "IoU", value: 0.62, threshold: "≥0.70", description: "TP/(TP + FP + FN)" },
  { category: "Regression", metric: "RMSE", value: 4.2, threshold: "≤5.0", description: "sqrt(MSE)" },
];

export default function ProjectResultViewPage() {
  const [tab, setTab] = useState(0);
  const router = useRouter();


  return (
    <>
      <Box p={4}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h5" sx={{ color: "#66717F" }} fontWeight="bold">
            AI Model Evaluation Results
          </Typography>

          <Button
            variant="outlined"
            className="text-transform-none"
            sx={{
              minWidth: "150px",
              color: "#66717F",
              
              fontWeight: "bold",
              borderColor: "#FA682C",
              "&:hover": {
                backgroundColor: "#FFF1EA",
              },
            }}

            onClick={() => router.push("/project-results")}
          >
            <ArrowBackIcon sx={{ mr: 1 }} />
            Back to Projects
          </Button>
        </Box>

        {/* Tabs */}
        <Box display="flex" mb={3}>
          <Button
            variant={tab === 0 ? "contained" : "outlined"}
            onClick={() => setTab(0)}
            sx={{
              mr: 2,
              backgroundColor: tab === 0 ? "#FA682C" : "transparent",
              color: tab === 0 ? "white" : "#66717F",
              borderColor: "#FA682C",
              "&:hover": {
                backgroundColor: tab === 0 ? "#E55A25" : "#FFF1EA",
              },
            }}
          >
            Table View
          </Button>
          <Button
            variant={tab === 1 ? "contained" : "outlined"}
            onClick={() => setTab(1)}
            sx={{
              backgroundColor: tab === 1 ? "#FA682C" : "transparent",
              color: tab === 1 ? "white" : "#66717F",
              borderColor: "#FA682C",
              "&:hover": {
                backgroundColor: tab === 1 ? "#E55A25" : "#FFF1EA",
              },
            }}
          >
            Graph View
          </Button>
        </Box>

        {/* Project Info */}
        <Box
          p={3}
          mb={3}
          sx={{
            backgroundColor: "#FFF5EB",
            borderRadius: 2,
            border: "1px solid #FA682C",
          }}
        >
          <Typography variant="h6" sx={{ color: "#66717F", mb: 2 }} fontWeight="bold">
            Project Information
          </Typography>
          <Box display="flex" flexWrap="wrap" gap={3}>
            <Box>
              <Typography variant="body2" sx={{ color: "#66717F", fontWeight: "bold" }}>
                Project Name:
              </Typography>
              <Typography variant="body2" sx={{ color: "#66717F" }}>
                Medical Image Classification Model
              </Typography>
            </Box>
            <Box>
              <Typography variant="body2" sx={{ color: "#66717F", fontWeight: "bold" }}>
                Model Type:
              </Typography>
              <Typography variant="body2" sx={{ color: "#66717F" }}>
                CNN-ResNet50
              </Typography>
            </Box>
            <Box>
              <Typography variant="body2" sx={{ color: "#66717F", fontWeight: "bold" }}>
                Version:
              </Typography>
              <Typography variant="body2" sx={{ color: "#66717F" }}>
                v2.1.0
              </Typography>
            </Box>
            <Box>
              <Typography variant="body2" sx={{ color: "#66717F", fontWeight: "bold" }}>
                Evaluation Date:
              </Typography>
              <Typography variant="body2" sx={{ color: "#66717F" }}>
                {new Date().toLocaleDateString("en-GB")}
              </Typography>
            </Box>
          </Box>
        </Box>


        {/* Content */}
        {tab === 0 ? (
          <Table>
            <TableHead>
              <TableRow>
                <TableCell><b>Category</b></TableCell>
                <TableCell><b>Metric</b></TableCell>
                <TableCell><b>Value</b></TableCell>
                <TableCell><b>Threshold</b></TableCell>
                <TableCell><b>Description</b></TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {tableData.map((row) => (
                <TableRow key={row.metric} sx={{ backgroundColor: tableData.indexOf(row) % 2 === 0 ? "#FFF5EB" : "white" }}>
                  <TableCell>{row.category}</TableCell>
                  <TableCell>{row.metric}</TableCell>
                  <TableCell>{row.value}</TableCell>
                  <TableCell>{row.threshold}</TableCell>
                  <TableCell>{row.description}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <Box p={2}>
            <Typography>Graph view coming soon...</Typography>
          </Box>
        )}

      </Box>
    </>
  );
}
