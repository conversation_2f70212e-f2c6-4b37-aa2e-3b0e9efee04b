"use client";

import type React from "react";
import { useState } from "react";
import {
	Box,
	Card,
	CardContent,
	Typography,
	Divider,
	IconButton,
	Button,
	Select,
	MenuItem,
} from "@mui/material";
import BoltIcon from "@mui/icons-material/Bolt";
import Rating from "@mui/material/Rating";
import clsx from "clsx";
import CustomButton from "../ui/Button";
import RatingModal from "./RatingModal";
import AvatarFallback from "../ui/AvatarFallback";
import { useRouter } from "next/navigation";
import type { CardProps } from "../interface/projectDocketInterface";
import MessageModal from "./MessageModal";
import ChatOutlinedIcon from "@mui/icons-material/ChatOutlined";
import { collaborator, getStatusColor, statusOptions } from "../../constants/fieldtype";
import { addlike, updateStatusDocket } from "../../services/desApiService";
import { toast } from "react-toastify";
import { sendforEvaluation } from "../../services/desApiService";
import RequestModal from "../projectDocket/RequestModal";

const ProjectCard: React.FC<CardProps> = ({
	projectId,
	modelName,
	gpu,
	version,
	type,
	specialty,
	subSpecialty,
	submittedOn,
	status,
	timeCount,
	role,
	isSelected = false,
	onSelect,
	canRead,
	datasetApi,
	average_rating,
	taggingSampleDataset,
}) => {
	const [isModalOpen, setIsModalOpen] = useState(false);
	const [isMessageModalOpen, setIsMessageModalOpen] = useState(false);
	const [modelOpen, setModelOpen] = useState(false);
	const [currentStatus, setCurrentStatus] = useState<number>(
		typeof status === "number" ? status : Number(status)
	);
	const router = useRouter();

	console.log("taggingSampleDataset", taggingSampleDataset);

	const handleLikeClick = async () => {
		try {
			const payload = { interaction_id: projectId };
			const response = await addlike(payload);
			console.log("Like added:", response.data);
			toast.success("You liked the Project Docket!");
			datasetApi();
		} catch (error) {
			console.error("Error adding like:", error);
			const errorMessage =
				error?.response?.data?.message || "An unexpected error occurred.";
			if (errorMessage.toLowerCase().includes("duplicate interaction")) {
				toast.error("You already liked the Project Docket.");
			} else {
				toast.error(errorMessage);
			}
		}
	};
	const handleStatusUpdate = async (projectId: string, newStatusCode: number) => {
		try {
			const statusLabel = statusOptions.find((option) => option.code === newStatusCode)?.label || "";

			await updateStatusDocket({
				id: projectId,
				status: statusLabel,
			});

			if (statusLabel === "RELEASED_FOR_EVALUATION") {
				await sendforEvaluation({ docket_uuid: projectId });
			}

			datasetApi();
		} catch (error) {
			console.error("Error updating status:", error);
		}
	};


	return (
		<>
		 <RequestModal open={modelOpen} onClose={() => setModelOpen(false)} projectId={projectId} modelName={modelName}/>
			{isMessageModalOpen && (
				<MessageModal
					open={isMessageModalOpen}
					onClose={() => setIsMessageModalOpen(false)}
					datasetId={projectId}
				/>
			)}
			<RatingModal
				open={isModalOpen}
				onClose={() => setIsModalOpen(false)}
				name={"Project Docket"}
				datasetId={projectId}
				datasetApi={datasetApi}
			/>
			<Card
				className={clsx(
					"shadow-sm transition-all border rounded-3xl",
					isSelected ? "border-orange-500" : "border-gray-200"
				)}
				onClick={onSelect}
			>
				<CardContent>
					<Box
						display="flex"
						flexDirection={{ xs: "column", lg: "row" }}
						justifyContent="space-between"
						alignItems={{ xs: "flex-start", sm: "center" }}
						gap={{ xs: 2, sm: 0 }}
					>
						<Box>
							<Box className="flex flex-col md:flex-row items-start md:items-center gap-2">
								<p className="font-bold font-[Open Sans]  text-[15px] md:text-[17px] whitespace-nowrap">
									Project ID: {projectId}
								</p>
								<Box className="ml-[15px]">
									<Rating value={average_rating} precision={1} readOnly sx={{
										'& .MuiRating-iconFilled': {
											color: '#16AFB5',
										},
										'& .MuiRating-iconEmpty': {
											color: '#16AFB5',
										},
									}} />
								</Box>
							</Box>
						</Box>
						<Box display="flex" gap={1} alignItems="center">
							{collaborator.includes(role) && (
								<Button
									variant="outlined"
									className="text-transform-none"
									sx={{
										minWidth: "100px",
										height: "35px",
										backgroundColor: "#FFFFFF",
										borderColor: "#F6692F",
										color: "#F6692F",
										"&:hover": {
											backgroundColor: "#FFF5E5",
											borderColor: "#FF8C00",
											color: "#FF8C00",
										},
										fontFamily: "Open Sans",
										fontWeight: "bold",
										fontSize: {
											xs: "10px",
											sm: "9px",
											md: "10px",
											lg: "13px",
											xl: "14px",
										},
									}}
									onClick={() => setIsModalOpen(true)}
								>
									Rate Docket
								</Button>
							)}
							<IconButton onClick={() => setIsMessageModalOpen(true)}>
								<ChatOutlinedIcon fontSize="medium" sx={{ color: "gray" }} />
							</IconButton>
							<Typography>
								Status:
								{collaborator.includes(role) ? (
									<Select
										value={currentStatus}
										onChange={(e) => {
											const newValue = Number(e.target.value);
											setCurrentStatus(newValue);
											handleStatusUpdate(projectId, newValue);
										}}
										size="small"
										sx={{
											backgroundColor: getStatusColor(currentStatus),
											fontWeight: 500,
											fontSize: "0.7rem",
											minWidth: "60px",
											maxWidth: "130px",
											height: "30px",
											".MuiSelect-select": {
												padding: "4px 8px",
											},
										}}
									>
										{statusOptions.map((option) => (
											<MenuItem key={option.code} value={option.code}>
												{option.label}
											</MenuItem>
										))}
									</Select>
								) : (
									<span
										className={clsx(
											"font-medium px-2 py-1 text-md ml-2 rounded-lg",
											getStatusColor(currentStatus),
										)}
									>
										{statusOptions.find((s) => String(s.code) === String(currentStatus))?.label || "Pending"}
									</span>
								)}
							</Typography>
						</Box>
					</Box>

					<Box
						mt={2}
						p={2}
						className="flex flex-col sm:flex-row justify-between items-end sm:items-start gap-4"
						sx={{
							backgroundColor: "#FFF4ED",
							border: "1px solid #D1D5DB",
							borderRadius: "8px",
							boxShadow: "0px 2px 8px rgba(0, 0, 0, 0.1)",
						}}
					>
						<Box display="flex" alignItems="center">
							<AvatarFallback className="w-[40px] h-[40px] sm:w-[50px] sm:h-[50px] md:w-[60px] md:h-[60px] lg:w-[70px] lg:h-[70px] p-2" name={modelName} />
							<Box ml={2}>
								<Typography sx={{ fontWeight: "600" }} className="text-orange-500 font-bold text-capitalize">
									{modelName}
								</Typography>
								<Typography className="text-sm text-gray-700 text-capitalize">
									{version}, {type}, {gpu}
									<br />
									{specialty}, {subSpecialty}, Submitted on: {submittedOn}
								</Typography>
							</Box>
						</Box>
						<Box display="flex" alignItems="center" gap={2}>
							
							{taggingSampleDataset && taggingSampleDataset === "" && (
								<CustomButton text="Request to SingHealth" onClick={() => setModelOpen(true)} isLoading={false} />
							)}

							{canRead && (
								<Box
									display="flex"
									alignItems="center"
									border={1}
									borderColor="#FB8C00"
									borderRadius={1}
									px={1}
								>
									<IconButton size="small" onClick={handleLikeClick}>
										<BoltIcon fontSize="small" className="text-orange-600" />
									</IconButton>
									<Divider orientation="vertical" flexItem className="bg-orange-400 h-6" />
									<Typography className="px-2 font-bold">{timeCount}</Typography>
								</Box>
							)}
							{status === "ready for review" && (
								<CustomButton
									text="View Result"
									onClick={() => router.push("/project-results/view")}
									isLoading={false}
								/>
							)}
						</Box>
					</Box>
				</CardContent>
			</Card>
			
		</>
	);
};

export default ProjectCard;
