"use client";

import { Box, Typography, Table, TableHead, TableRow, TableCell, TableBody, Button } from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { useState } from "react";
import { useRouter } from "next/navigation";


const tableData = [
  { category: "Classification", metric: "Accuracy", value: 0.89, threshold: "≥0.85", description: "(TP + TN)/(Total Samples)" },
  { category: "Classification", metric: "F1-Score", value: 0.79, threshold: "≥0.80", description: "2*(Precision*Recall)/(Precision+Recall)" },
  { category: "Segmentation", metric: "IoU", value: 0.62, threshold: "≥0.70", description: "TP/(TP + FP + FN)" },
  { category: "Regression", metric: "RMSE", value: 4.2, threshold: "≤5.0", description: "sqrt(MSE)" },
];

export default function ProjectResultPage() {
  const [tab, setTab] = useState(0);
  const router = useRouter();


  return (
    <>
      <Box p={4}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h5" sx={{ color: "#66717F" }} fontWeight="bold">
            AI Model Evaluation Results
          </Typography>

          <Button
            variant="outlined"
            className="text-transform-none"
            sx={{
              minWidth: "150px",
              color: "#66717F",
              
              fontWeight: "bold",
              borderColor: "#FA682C",
              "&:hover": {
                backgroundColor: "#FFF1EA",
              },
            }}

            onClick={() => router.push("/project-results")}

          >
            <ArrowBackIcon fontSize="small" />
            Back</Button>
        </Box>
        {/* Project Info */}
        <Box
          display="grid"
          gridTemplateColumns="repeat(4, 1fr)"
          border="2px solid #F97316"
          borderRadius="8px"
          mb={3}
        >
          {["Project Name", "Speciality", "Sub-Speciality", "Type of the Model"].map((label) => (
            <Box
              key={label}
              borderRight={label !== "Type of the Model" ? "1px solid #F97316" : "none"}
              display="flex"
              flexDirection="column"
              justifyContent="center"
              alignItems="center"
              p={2}
            >
              <Typography fontWeight={600} align="center">{label}</Typography>
              <Typography variant="body2" align="center">Lorem Ipsum knknkcskv vxc</Typography>
            </Box>
          ))}
        </Box>
        <Box
          mb={2}
          display="flex"
          gap={1.5}
          width="fit-content"
        >
          <Button
            onClick={() => setTab(0)}
            variant={tab === 0 ? "contained" : "outlined"}
            className="text-transform-none"
            sx={{
              bgcolor: tab === 0 ? "#F97316" : "#ffffff",
              color: tab === 0 ? "#ffffff" : "#F97316",
              borderColor: "#F97316",
              fontWeight: "bold",
              minWidth: 140,
              borderRadius: "8px",
              boxShadow: tab === 0 ? "0px 4px 10px rgba(249, 115, 22, 0.2)" : "none",
              "&:hover": {
                bgcolor: tab === 0 ? "#f97316cc" : "#fff5eb",
                borderColor: "#F97316",
              },
            }}
          >
            Table View
          </Button>
          <Button
            onClick={() => setTab(1)}
            variant={tab === 1 ? "contained" : "outlined"}
            className="text-transform-none"
            sx={{
              bgcolor: tab === 1 ? "#F97316" : "#ffffff",
              color: tab === 1 ? "#ffffff" : "#F97316",
              borderColor: "#F97316",
              fontWeight: "bold",
              
              minWidth: 140,
              borderRadius: "8px",
              boxShadow: tab === 1 ? "0px 4px 10px rgba(249, 115, 22, 0.2)" : "none",
              "&:hover": {
                bgcolor: tab === 1 ? "#f97316cc" : "#fff5eb",
                borderColor: "#F97316",
              },
            }}
          >
            Graph View
          </Button>
        </Box>


        {/* Content */}
        {tab === 0 ? (
          <Table>
            <TableHead>
              <TableRow>
                <TableCell><b>Category</b></TableCell>
                <TableCell><b>Metric</b></TableCell>
                <TableCell><b>Value</b></TableCell>
                <TableCell><b>Threshold</b></TableCell>
                <TableCell><b>Description</b></TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {tableData.map((row) => (
                <TableRow key={row.metric} sx={{ backgroundColor: tableData.indexOf(row) % 2 === 0 ? "#FFF5EB" : "white" }}>
                  <TableCell>{row.category}</TableCell>
                  <TableCell>{row.metric}</TableCell>
                  <TableCell>{row.value}</TableCell>
                  <TableCell>{row.threshold}</TableCell>
                  <TableCell>{row.description}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <Box p={2}>
            <Typography>Graph view coming soon...</Typography>
          </Box>
        )}

      </Box>
    </>
  );
}
