'use client';

import { useEffect, useMemo } from "react";
import { usePathname, useRouter } from "next/navigation";
import { useAppSelector } from "../../store/store";
import Layout from "./dashboardHeader/Layout";
import { superadmin } from "../../constants/fieldtype";
import { ThemeProvider, CssBaseline } from "@mui/material";
import theme from "../../theme";

const moduleNameToPathMap: Record<string, string> = {
  "Dashboard": "/dashboard",
  // "Organizations": "/organizations",
  "Project Dockets": "/project-results",
  "Datasets": "/datasets",
  // "User Management": "/user-management",
  // "Audit Logs": "/audit-logs",
  "Forms": "/forms",
  // "Request Approval": "/request-approval",
  // "Role & Access": "/role-access",
  // "Settings": "/settings",
  "profile": "/profile",
};

export default function ProtectedLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const router = useRouter();

  interface RootState {
    accessRights?: {
      accessRightsList?: {
        modules?: Array<{
          module_name: string;
          permissions?: Array<{ permission_name: string }>;
        }>;
      };
    };
    user?: {
      userDetail?: {
        role?: {
          name?: string;
        };
      };
    };
  }

  const permissions = useAppSelector((state: RootState) =>
    state?.accessRights?.accessRightsList?.modules?.map((mod) => ({
      module_name: mod.module_name,
      permissions: mod.permissions?.map((perm) => perm.permission_name) || [],
    }))
  );
     const isSuperAdmin = useAppSelector(
         (state: RootState) =>
             Array.isArray(superadmin)
                 ? superadmin.includes(state?.user?.userDetail?.role?.name)
                 : state?.user?.userDetail?.role?.name === superadmin,
     ) as boolean | undefined;

 const isAllowed = useMemo(() => {
  if (isSuperAdmin) return true; 
  if (!pathname || !permissions) return false;

  return permissions.some(({ module_name, permissions: perms }) => {
    const basePath = moduleNameToPathMap[module_name];
    if (!basePath) return false;

    if (pathname === basePath) return true;
    if (pathname.startsWith(`${basePath}/create`) && perms.includes("create")) return true;
    if (pathname.startsWith(`${basePath}/edit`) && perms.includes("update")) return true;
    if (pathname.startsWith(`${basePath}/view`) && perms.includes("read")) return true;
    return false;
  });
}, [superadmin, pathname, permissions]);


  useEffect(() => {
    if (!isAllowed) {
      router.replace("/unauthorized");
    }
  }, [isAllowed, router]);

  if (!isAllowed) {
    return null;
  }

   return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Layout>
        {children}
      </Layout>
    </ThemeProvider>
  );
}
